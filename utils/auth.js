// 用户认证工具类
class Auth {
  constructor() {
    this.tokenKey = 'token';
    this.userInfoKey = 'userInfo';
  }

  // 获取token
  getToken() {
    return wx.getStorageSync(this.tokenKey);
  }

  // 设置token
  setToken(token) {
    wx.setStorageSync(this.tokenKey, token);
  }

  // 获取用户信息
  getUserInfo() {
    return wx.getStorageSync(this.userInfoKey);
  }

  // 设置用户信息
  setUserInfo(userInfo) {
    wx.setStorageSync(this.userInfoKey, userInfo);
  }

  // 检查是否已登录
  isLoggedIn() {
    const token = this.getToken();
    const userInfo = this.getUserInfo();
    return !!(token && userInfo);
  }

  // 登录
  login(token, userInfo) {
    this.setToken(token);
    this.setUserInfo(userInfo);
  }

  // 登出
  logout() {
    wx.removeStorageSync(this.tokenKey);
    wx.removeStorageSync(this.userInfoKey);
  }

  // 更新用户信息
  updateUserInfo(userInfo) {
    const currentUserInfo = this.getUserInfo();
    const updatedUserInfo = { ...currentUserInfo, ...userInfo };
    this.setUserInfo(updatedUserInfo);
  }

  // 检查登录状态，未登录则显示提示（不再强制跳转）
  checkLoginAndRedirect() {
    if (!this.isLoggedIn()) {
      return false;
    }
    return true;
  }

  // 检查登录状态，未登录则显示注册提示弹窗
  checkLoginAndShowRegisterPrompt() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    console.log('[登录检查] token:', token ? '存在' : '不存在');
    console.log('[登录检查] userInfo:', userInfo ? '存在' : '不存在');

    // 清除无效的登录状态（如果只有token但没有userInfo，或者userInfo没有id）
    if (token && (!userInfo || !userInfo.id)) {
      console.log('[登录检查] 清除无效的登录状态');
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
    }

    // 重新获取清理后的状态
    const cleanToken = wx.getStorageSync('token');
    const cleanUserInfo = wx.getStorageSync('userInfo');
    console.log('[登录检查] 清理后 token:', cleanToken ? '存在' : '不存在');
    console.log('[登录检查] 清理后 userInfo:', cleanUserInfo ? '存在' : '不存在');

    // 严格检查：必须有token和userInfo，且userInfo必须有id
    if (!cleanToken || !cleanUserInfo || !cleanUserInfo.id) {
      console.log('[登录检查] 用户未登录，显示注册提示弹窗');
      wx.showModal({
        title: '陪诊师信息涉及隐私',
        content: '如需下单可前往注册或随后前往"我的"页面注册',
        confirmText: '立即注册',
        cancelText: '暂不',
        success: (res) => {
          if (res.confirm) {
            // 用户选择立即注册，传递来源页面信息
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const currentRoute = currentPage ? currentPage.route : '';
            const currentOptions = currentPage ? currentPage.options : {};

            // 构建来源页面参数
            let fromPage = currentRoute;
            if (Object.keys(currentOptions).length > 0) {
              const queryString = Object.keys(currentOptions)
                .map(key => `${key}=${encodeURIComponent(currentOptions[key])}`)
                .join('&');
              fromPage += `?${queryString}`;
            }

            wx.navigateTo({
              url: `/pages/user-register/user-register?fromPage=${encodeURIComponent(fromPage)}`
            });
          }
          // 如果用户选择暂不，不做任何操作，允许继续浏览
        }
      });
      return false;
    }
    console.log('[登录检查] 用户已登录，继续执行');
    return true;
  }

  // 获取请求头（包含token）
  getHeaders() {
    const token = this.getToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  // 验证订单权限：确保用户只能访问自己的订单
  validateOrderPermission(order, userType) {
    const userInfo = this.getUserInfo();
    if (!userInfo) return false;

    if (userType === 'staff' || userType === 'medical_staff') {
      // 医护人员只能查看分配给自己的订单
      return order.staffId === userInfo.staffId;
    } else if (userType === 'patient') {
      // 病患用户只能查看自己的订单
      return order.userId === userInfo.id;
    }

    return false;
  }

  // 验证订单列表权限：过滤订单列表
  filterOrdersByPermission(orders, userType) {
    const userInfo = this.getUserInfo();
    if (!userInfo || !orders) return [];

    if (userType === 'staff' || userType === 'medical_staff') {
      // 医护人员只能查看分配给自己的订单
      return orders.filter(order => order.staffId === userInfo.staffId);
    } else if (userType === 'patient') {
      // 病患用户只能查看自己的订单
      return orders.filter(order => order.userId === userInfo.id);
    }

    return [];
  }
}

// 创建单例实例
const auth = new Auth();

// 存储用户信息
function setUserInfo(user) {
  wx.setStorageSync('userInfo', user);
}
// 获取用户信息
function getUserInfo() {
  return wx.getStorageSync('userInfo') || {};
}
// 判断是否已登录
function isLoggedIn() {
  return !!wx.getStorageSync('token') && !!wx.getStorageSync('userInfo');
}

// 独立函数：检查登录状态，未登录则显示提示（不再强制跳转）
function checkLoginAndRedirect() {
  const token = wx.getStorageSync('token');
  if (!token) {
    return false;
  }
  return true;
}

// 封装带token的request方法
function requestWithToken(options) {
  const token = wx.getStorageSync('token');
  const headers = Object.assign({
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }, options.header || {});
  wx.request({
    ...options,
    header: headers
  });
}

// 全局统一 token 拦截和注入
function withAuth(options = {}) {
  const token = wx.getStorageSync('token');
  if (!token) {
    return Promise.reject('未登录');
  }
  options.header = Object.assign({}, options.header, {
    'Authorization': 'Bearer ' + token
  });
  return options;
}

module.exports = {
  auth,
  setUserInfo,
  getUserInfo,
  isLoggedIn,
  checkLoginAndRedirect,
  checkLoginAndShowRegisterPrompt: auth.checkLoginAndShowRegisterPrompt.bind(auth),
  requestWithToken,
  withAuth
};
