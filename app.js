import gulpError from './utils/gulpError';
const messageManager = require('./utils/messageManager.js');
const auth = require('./utils/auth.js');
const eventBus = require('./utils/eventBus.js');
const configManager = require('./utils/configManager.js');
const { API_BASE } = require('./api/base.js');

App({
    globalData: {
        eventBus: eventBus,
        unreadCount: 0,
        config: null
    },
    
    onLaunch() {
        console.log('[App] onLaunch 被调用');

        // 加载配置文件
        this.loadGlobalConfig();

        // 立即执行静默登录，不再延迟
        console.log('[App] 开始执行静默登录');
        this.silentLogin();
        
        // 应用启动时检查登录状态，如果已登录则启动消息管理
        if (auth.isLoggedIn()) {
            messageManager.start();
        }
        
        // 监听消息更新，同步到全局数据
        eventBus.on('messagesUpdated', (messages, unreadCount) => {
            this.globalData.unreadCount = unreadCount;
        });
        
        eventBus.on('tabBarRedDotUpdate', (unreadCount) => {
            this.globalData.unreadCount = unreadCount;
        });
    },
    
    onShow() {
        if (gulpError !== 'gulpErrorPlaceHolder') {
            wx.redirectTo({
                url: `/pages/gulp-error/index?gulpError=${gulpError}`,
            });
        }
        
        // 应用显示时检查登录状态，如果已登录则启动消息管理
        if (auth.isLoggedIn()) {
            messageManager.start();
        }
    },
    
    onHide() {
        // 应用隐藏时停止消息管理以节省资源
        messageManager.stop();
    },
    
    /**
     * 加载全局配置
     */
    async loadGlobalConfig() {
        try {
            console.log('[App] 开始加载全局配置...');
            const config = await configManager.loadConfig();
            this.globalData.config = config;
            console.log('[App] 全局配置加载完成:', config);
        } catch (error) {
            console.error('[App] 全局配置加载失败:', error);
        }
    },

    silentLogin() {
        wx.login({
            success: (res) => {
                console.log('[静默登录] wx.login code:', res.code);
                if (res.code) {
                    wx.request({
                        url: `${API_BASE}/system/auth/miniprogram_login`,
                        method: 'POST',
                        data: { code: res.code },
                        header: { 'content-type': 'application/json' },
                        success: (resp) => {
                            console.log('[静默登录] 接口返回:', resp.data);
                            if (resp.data && resp.data.success && resp.data.token) {
                                wx.setStorageSync('token', resp.data.token);
                                wx.setStorageSync('userInfo', resp.data.user);
                                // 如果当前页面是登录页，自动跳转首页
                                const pages = getCurrentPages();
                                if (pages.length && pages[pages.length - 1].route === 'pages/login/login') {
                                    console.log('[静默登录] 已注册，自动跳转首页');
                                    wx.switchTab({ url: '/pages/home/<USER>' });
                                }
                            } else {
                                console.log('[静默登录] 未注册或登录失败，允许继续浏览:', resp.data);
                                // 不再处理未注册用户，允许继续浏览
                            }
                        },
                        fail: (err) => {
                            console.error('[静默登录] wx.request 失败:', err);
                        }
                    });
                } else {
                    console.error('[静默登录] wx.login 无code:', res);
                }
            },
            fail: (err) => {
                console.error('[静默登录] wx.login 失败:', err);
            }
        });
    }
});
