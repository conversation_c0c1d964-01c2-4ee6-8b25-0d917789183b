const { API_BASE } = require('../../api/base.js');

Page({
  data: {
    phone: '',
    phoneError: '', // 手机号错误信息
    phoneValid: false, // 手机号是否有效
    userTypeOptions: [
      { label: '患者', value: 'patient' },
      { label: '医护人员', value: 'medical_staff' }
    ],
    userTypeIndex: 0,
    loading: false,
    openid: '',
    navbarHeight: 0, // 导航栏高度
    fromPage: '' // 来源页面
  },

  onLoad(options) {
    console.log('[用户注册] onLoad options:', options);
    // 支持通过页面参数传递 openid
    if (options && options.openid) {
      this.setData({ openid: options.openid });
    }
    // 记录来源页面
    if (options && options.fromPage) {
      this.setData({ fromPage: decodeURIComponent(options.fromPage) });
      console.log('[用户注册] 来源页面:', decodeURIComponent(options.fromPage));
    }
  },

  // 导航栏就绪事件处理
  onNavbarReady(e) {
    const { navbarHeight } = e.detail;
    this.setData({ navbarHeight });
  },

  onUserTypeChange(e) {
    this.setData({ userTypeIndex: Number(e.detail.value) });
  },
  onPhoneInput(e) {
    const phone = e.detail.value;
    this.setData({ phone });
    this.validatePhone(phone);
  },

  // 手机号校验函数
  validatePhone(phone) {
    let phoneError = '';
    let phoneValid = false;

    // 清空手机号
    if (!phone) {
      phoneError = '请输入手机号';
      phoneValid = false;
    }
    // 检查长度
    else if (phone.length !== 11) {
      phoneError = '手机号长度应为11位';
      phoneValid = false;
    }
    // 检查格式
    else if (!/^1[3-9]\d{9}$/.test(phone)) {
      phoneError = '请输入正确的手机号格式';
      phoneValid = false;
    }
    // 检查运营商号段
    else if (!this.isValidPhonePrefix(phone)) {
      phoneError = '请输入有效的手机号';
      phoneValid = false;
    }
    else {
      phoneError = '';
      phoneValid = true;
    }

    this.setData({ phoneError, phoneValid });
  },

  // 检查手机号运营商号段
  isValidPhonePrefix(phone) {
    const prefix = phone.substring(0, 3);
    const validPrefixes = [
      // 中国移动
      '134', '135', '136', '137', '138', '139', '147', '148', '150', '151', '152', '157', '158', '159', '172', '178', '182', '183', '184', '187', '188', '195', '198',
      // 中国联通
      '130', '131', '132', '145', '146', '155', '156', '166', '167', '171', '175', '176', '185', '186',
      // 中国电信
      '133', '149', '153', '173', '174', '177', '180', '181', '189', '191', '193', '199',
      // 虚拟运营商
      '170', '171', '172', '173', '174', '175', '176', '177', '178'
    ];
    return validPrefixes.includes(prefix);
  },

  onRegister() {
    const { phone, phoneValid, userTypeOptions, userTypeIndex, openid } = this.data;
    const user_type = userTypeOptions[userTypeIndex].value;
    
    // 检查手机号是否有效
    if (!phoneValid) {
      wx.showToast({ 
        title: this.data.phoneError || '请输入正确的手机号', 
        icon: 'none',
        duration: 2000
      });
      return;
    }
    this.setData({ loading: true });
    wx.login({
      success: (res) => {
        if (res.code) {
          const data = {
            code: res.code,
            user_type,
            phone
          };
          if (openid) data.openid = openid;
          wx.request({
            url: `${API_BASE}/system/auth/miniprogram_register`,
            method: 'POST',
            data,
            header: { 'content-type': 'application/json' },
            success: (resp) => {
              this.setData({ loading: false });
              if (resp.data && resp.data.success && resp.data.token) {
                wx.setStorageSync('token', resp.data.token);
                wx.setStorageSync('userInfo', resp.data.user);
                wx.showToast({ title: '注册成功' });

                setTimeout(() => {
                  // 如果有来源页面，返回到来源页面
                  if (this.data.fromPage) {
                    console.log('[用户注册] 注册成功，返回来源页面:', this.data.fromPage);
                    // 先返回到上一页，然后重新加载该页面以更新登录状态
                    wx.navigateBack({
                      success: () => {
                        // 返回成功后，通过事件通知页面刷新登录状态
                        const pages = getCurrentPages();
                        const prevPage = pages[pages.length - 1];
                        if (prevPage && prevPage.onShow) {
                          prevPage.onShow();
                        }
                      },
                      fail: () => {
                        // 如果返回失败，重新导航到来源页面
                        if (this.data.fromPage.startsWith('/')) {
                          wx.redirectTo({ url: this.data.fromPage });
                        } else {
                          wx.reLaunch({ url: '/pages/home/<USER>' });
                        }
                      }
                    });
                  } else {
                    // 没有来源页面，跳转到首页
                    wx.reLaunch({ url: '/pages/home/<USER>' });
                  }
                }, 1000);
              } else {
                wx.showToast({ title: resp.data.error || '注册失败', icon: 'none' });
              }
            },
            fail: () => {
              this.setData({ loading: false });
              wx.showToast({ title: '注册请求失败', icon: 'none' });
            }
          });
        } else {
          this.setData({ loading: false });
          wx.showToast({ title: '获取code失败', icon: 'none' });
        }
      },
      fail: () => {
        this.setData({ loading: false });
        wx.showToast({ title: 'wx.login失败', icon: 'none' });
      }
    });
  }
}); 