// pages/services/list.js
import navbarBehavior from '../../behaviors/navbar';
import { servicesList, categories } from './data/index.js';

Page({
  behaviors: [navbarBehavior],
  data: {
    servicesList: [],
    categories: [],
    filteredList: [],
    selectedCategory: '全部',
    loading: false
  },
  
  onLoad() {
    console.log('服务列表页面加载');
    this.initData();
  },
  
  initData() {
    this.setData({
      servicesList: servicesList,
      categories: [{ name: '全部', icon: 'apps', color: '#666' }, ...categories],
      filteredList: servicesList,
      loading: false
    });
  },
  
  // 分类筛选
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category;
    console.log('选择分类:', category);
    
    this.setData({ selectedCategory: category });
    
    if (category === '全部') {
      this.setData({ filteredList: this.data.servicesList });
    } else {
      const filtered = this.data.servicesList.filter(service => 
        service.category === category
      );
      this.setData({ filteredList: filtered });
    }
  },
  
  // 服务点击
  onServiceTap(e) {
    const { path, name } = e.currentTarget.dataset;
    console.log('[服务列表] 点击服务:', name, path);

    // 特殊路径处理
    if (path === 'diary') {
      wx.navigateTo({ url: '/pages/diary/list' });
    } else if (path === 'orders') {
      wx.navigateTo({ url: '/pages/orders/orders' });
    } else if (path.startsWith('/pages/')) {
      wx.navigateTo({ url: path });
    } else {
      console.error('未知的服务路径:', path);
      wx.showToast({ title: '功能开发中', icon: 'none' });
    }
  },
  
  // 搜索服务
  onSearchInput(e) {
    const keyword = e.detail.value.trim();
    console.log('搜索关键词:', keyword);
    
    if (!keyword) {
      this.setData({ filteredList: this.data.servicesList });
      return;
    }
    
    const filtered = this.data.servicesList.filter(service => 
      service.name.includes(keyword) || 
      service.description.includes(keyword) ||
      service.category.includes(keyword)
    );
    
    this.setData({ filteredList: filtered });
  }
}); 