import navbarBehavior from '../../behaviors/navbar';

Page({
  behaviors: [navbarBehavior],
  
  data: {
    showAgentAction: false,
    userInfo: {},
  },
  onLoad() {
    const auth = require('../../utils/auth.js');
    const userInfo = auth.getUserInfo() || {};
    this.setData({ userInfo });
  },
  
  onShow() {
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      const userInfo = wx.getStorageSync('userInfo') || {};
      const isStaff = userInfo.user_type === 'staff' || userInfo.user_type === 'medical_staff';
      // staff用户：工作台(0)、订单(1)、消息(2)、我的(3)
      // 普通用户：首页(0)、服务(1)、订单(2)、消息(3)、我的(4)
      const selected = isStaff ? 1 : 1; // 普通用户的服务页面
      this.getTabBar().setData({
        selected
      });
    }
  },
  goAccompany() {
    const auth = require('../../utils/auth.js');
    if (auth.checkLoginAndShowRegisterPrompt()) {
      wx.navigateTo({ url: '/pages/accompany/accompany' });
    }
  },
  onAgentTap() {
    this.setData({ showAgentAction: true });
  },
  closeAgentAction() {
    this.setData({ showAgentAction: false });
  },
  goHospitalize() {
    const auth = require('../../utils/auth.js');
    if (auth.checkLoginAndShowRegisterPrompt()) {
      wx.navigateTo({ url: '/pages/hospitalize/hospitalize' });
    }
    this.closeAgentAction();
  },
  goRegister() {
    const auth = require('../../utils/auth.js');
    if (auth.checkLoginAndShowRegisterPrompt()) {
      wx.navigateTo({ url: '/pages/register/register' });
    }
    this.closeAgentAction();
  },
  goMedicine() {
    const auth = require('../../utils/auth.js');
    if (auth.checkLoginAndShowRegisterPrompt()) {
      wx.navigateTo({ url: '/pages/medicine-order/medicine-order' });
    }
  },

  // 新增：定制服务
  goCustom() {
    wx.showModal({
      title: '定制服务',
      content: '如需个性化医疗陪护服务，请联系客服：400-123-4567',
      showCancel: false,
      confirmText: '确定'
    });
  },
  goPayTest() {
    console.log('[服务页] 点击微信支付测试入口，准备跳转');
    wx.navigateTo({ url: '/pages/paytest/paytest' });
  },
  stop() {}
}); 