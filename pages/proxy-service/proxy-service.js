// pages/proxy-service/proxy-service.js
import navbarBehavior from '../../behaviors/navbar';

Page({
  behaviors: [navbarBehavior],
  data: {
    proxyServices: [
      {
        name: '代办问诊',
        description: '专业陪诊师代办问诊，提供全程贴心服务',
        icon: 'user-business',
        iconType: 'tdesign',
        path: '/pages/accompany/accompany',
        features: ['专业陪诊', '全程陪同', '贴心服务', '经验丰富'],
        color: '#68C4C1'
      },
      {
        name: '代办买药',
        description: '代取药品，专业配送服务',
        icon: 'hospital',
        iconType: 'tdesign',
        path: '/pages/medicine-order/medicine-order',
        features: ['代取药品', '专业配送', '安全可靠', '快速送达'],
        color: '#FF6B35'
      },
      {
        name: '诊前挂号',
        description: '提前预约挂号，避免排队等待',
        icon: 'calendar',
        iconType: 'tdesign',
        path: '/pages/register/register',
        features: ['提前预约', '免排队', '专业代办', '快速挂号'],
        color: '#4CAF50'
      }
    ]
  },

  onLoad() {
    console.log('代办服务页面加载');
  },

  onServiceTap(e) {
    const { path } = e.currentTarget.dataset;
    if (path && path !== 'placeholder') {
      const auth = require('../../utils/auth.js');
      if (auth.checkLoginAndShowRegisterPrompt()) {
        wx.navigateTo({ url: path });
      }
    } else {
      wx.showToast({ title: '功能开发中', icon: 'none' });
    }
  },

  // 联系客服
  contactService() {
    wx.showToast({ 
      title: '请联系客服微信：medcompanion', 
      icon: 'none',
      duration: 3000
    });
  }
}); 