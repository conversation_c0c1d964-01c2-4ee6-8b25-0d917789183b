// pages/packages/detail.js
import navbarBehavior from '../../behaviors/navbar';
const { getPackageDetail, getBookingPageByType } = require('../../api/packages.js');
const auth = require('../../utils/auth.js');

Page({
  behaviors: [navbarBehavior],
  
  data: {
    packageInfo: null,
    loading: false,
    packageId: null,
    type: 'accompaniment'
  },

  onLoad(options) {
    console.log('套餐详情页面 onLoad, options:', options);

    const packageId = options.id;
    const type = options.type || 'accompaniment';

    if (!packageId) {
      wx.showToast({
        title: '套餐信息错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      packageId,
      type
    });

    // 加载套餐详情
    this.loadPackageDetail();
  },

  onShow() {
    // 页面显示时检查登录状态，用于用户注册成功返回后更新状态
    console.log('[套餐详情] 页面显示，检查登录状态');
    const isLoggedIn = auth.isLoggedIn();
    console.log('[套餐详情] 当前登录状态:', isLoggedIn);
  },

  // 加载套餐详情
  loadPackageDetail() {
    this.setData({ loading: true });
    
    getPackageDetail(this.data.packageId).then(packageInfo => {
      console.log('获取套餐详情成功:', packageInfo);
      
      // 处理套餐数据
      const processedPackage = {
        ...packageInfo,
        // 添加默认图片
        image: packageInfo.image || '/assets/medical-5459653_960_720.webp',
        // 格式化价格 - 使用实际返回的price字段
        formattedPrice: this.formatPrice(packageInfo.price),
        // 保留原始HTML描述用于富文本显示
        richDescription: packageInfo.description || '',
        // 同时保留清理版本用于备用
        cleanDescription: this.cleanHtml(packageInfo.description || ''),
        // 处理内容详情 - contentDetails现在是字符串
        richContentDetails: packageInfo.contentDetails || ''
      };
      
      this.setData({ 
        packageInfo: processedPackage,
        loading: false 
      });
    }).catch(err => {
      console.error('获取套餐详情失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取套餐详情失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },

  // 格式化价格
  formatPrice(price) {
    if (!price && price !== 0) return '0';
    return Number(price).toFixed(0);
  },

  // 清理HTML标签
  cleanHtml(html) {
    if (!html) return '';
    return html.replace(/<[^>]*>/g, '');
  },



  // 立即预约
  onBookNow() {
    console.log('[套餐详情] 点击立即预约按钮');

    if (!this.data.packageInfo) {
      wx.showToast({
        title: '套餐信息加载中',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态，未登录则显示注册提示弹窗
    console.log('[套餐详情] 开始检查登录状态');
    const result = auth.checkLoginAndShowRegisterPrompt();
    console.log('[套餐详情] 登录检查结果:', result);

    if (!result) {
      console.log('[套餐详情] 登录检查失败，无法预约');
      return;
    }

    console.log('[套餐详情] 登录检查通过，准备跳转到预约页面');

    // 获取对应的预约页面路径
    const bookingPage = getBookingPageByType(this.data.type);
    console.log('[套餐详情] 预约页面路径:', bookingPage);

    // 跳转到预约页面，传递套餐ID
    wx.navigateTo({
      url: `${bookingPage}?packageId=${this.data.packageId}&type=${this.data.type}`
    });
  },

  // 分享套餐
  onShareAppMessage() {
    return {
      title: this.data.packageInfo ? this.data.packageInfo.name : '医疗陪诊套餐',
      path: `/pages/packages/detail?id=${this.data.packageId}&type=${this.data.type}`,
      imageUrl: this.data.packageInfo ? this.data.packageInfo.image : ''
    };
  }
});
