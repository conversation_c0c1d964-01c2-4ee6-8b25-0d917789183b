import themeChangeBehavior from 'tdesign-miniprogram/mixins/theme-change';
import navbarBehavior from '../../behaviors/navbar';
const hospitalsMock = require('../../mock/hospital.js');
const staffMock = require('../../mock/staff.js');
const auth = require('../../utils/auth.js');
const { API_BASE } = require('../../api/base.js');
import { list, skylineList } from './data/index';
const { getPatientList } = require('../../api/patients.js');
Page({
    behaviors: [themeChangeBehavior, navbarBehavior],
    data: {
        list: [],
        hospitals: [],
        staffs: [],
        currentYear: new Date().getFullYear(),
        isSkyline: false,
        unreadCount: 0,
        isLoggedIn: false,
        userInfo: null,
        // 轮播图数据
        swiperList: [
            {
                value: 'https://medcompanion.enginorigin.com/uploads/banner1.jpeg',
                ariaLabel: '医疗陪诊服务'
            },
            {
                value: 'https://medcompanion.enginorigin.com/uploads/banner2.jpeg',
                ariaLabel: '专业医疗团队'
            },
            {
                value: 'https://medcompanion.enginorigin.com/uploads/banner3.jpeg',
                ariaLabel: '贴心陪诊体验'
            },
            {
                value: 'https://medcompanion.enginorigin.com/uploads/banner4.jpeg',
                ariaLabel: '专业医疗咨询'
            },
            {
                value: 'https://medcompanion.enginorigin.com/uploads/banner5.jpeg',
                ariaLabel: '优质医疗服务'
            }
        ],
        swiperCurrent: 0, // 确保从第一张开始
        swiperAutoplay: true,
        swiperDuration: 800, // 增加过渡时间，使切换更平滑
        swiperInterval: 4000, // 增加自动播放间隔，减少频繁切换
        // 陪诊日记数据
        diaries: [],
        diaryLoading: false,
        // 轮播图状态控制
        swiperReady: false,
        swiperResetTimer: null,
        // 新增：轮播图重置状态控制
        swiperInitialized: false,
        lastShowTime: 0,
        // 新增：页面隐藏状态标记
        isPageHidden: false
    },
    onLoad(options) {
        const { path, q, skyline } = options;
        console.log('首页 onLoad options:', options);
        
        // 检查登录状态和用户类型
        this.checkLoginStatus();
        const userInfo = auth.getUserInfo();
        
        // 如果是staff用户，直接跳转到工作台
        if (userInfo && (userInfo.user_type === 'staff' || userInfo.user_type === 'medical_staff')) {
            console.log('[LOG] staff用户，跳转到工作台');
            wx.switchTab({
                url: '/pages/staff/dashboard/dashboard'
            });
            return;
        }
        
        // 如果是病患用户，检查是否有可用的病患资料
        if (userInfo && userInfo.user_type === 'patient') {
            console.log('[LOG] 病患用户，检查病患资料');
            this.checkPatientAvailability();
        }
        
        let compList = [];
        this.skyline = skyline;
        if (this.skyline) {
            compList = skylineList;
        }
        else {
            compList = list;
        }
        
        this.setData({
            list: compList,
            isSkyline: !!skyline,
            hospitals: [], // 初始化为空，后端请求填充
            staffs: [], // staff不显示推荐陪诊师
        });
        if (q) {
            const str = this.getQueryByUrl(decodeURIComponent(q));
            console.log(str, str.page);
            wx.navigateTo({
                url: `/pages/${str.page}/${str.page}`,
            });
        }
        this.trdPrivacy = this.selectComponent('#trdPrivacy');
        // 用户类型过滤入口
        console.log('首页 userInfo:', auth.getUserInfo());
        console.log('首页 user_type:', auth.getUserInfo() && auth.getUserInfo().user_type);
        // 新增log
        if (auth.getUserInfo()) {
            console.log('[LOG] 当前用户类型:', auth.getUserInfo().user_type);
        } else {
            console.log('[LOG] 当前未登录');
        }
        let filteredList = compList;
        if (auth.getUserInfo() && auth.getUserInfo().user_type === 'medical_staff') {
            filteredList = [];
            console.log('[LOG] medical_staff用户，服务网格已隐藏');
        }
        this.setData({
            list: filteredList,
            isSkyline: !!skyline,
            hospitals: [], // 初始化为空，后端请求填充
            staffs: [], // staff不显示推荐陪诊师
            userInfo: auth.getUserInfo() || {},
        });
        // 只为非staff请求推荐医院和陪诊师
        if (!auth.getUserInfo() || auth.getUserInfo().user_type !== 'medical_staff') {
            this.loadHospitalData();
            this.loadStaffData();
        }
    },
    
    onShow() {
        // 每次显示页面时检查登录状态
        this.checkLoginStatus();
        
        // 检查用户类型，如果是staff用户则跳转到工作台
        const userInfo = auth.getUserInfo();
        if (userInfo && (userInfo.user_type === 'staff' || userInfo.user_type === 'medical_staff')) {
            console.log('[LOG] staff用户，跳转到工作台');
            wx.switchTab({
                url: '/pages/staff/dashboard/dashboard'
            });
            return;
        }
        
        // 更新tabBar选中状态
        if (typeof this.getTabBar === 'function' && this.getTabBar()) {
            this.getTabBar().setData({
                selected: 0
            });
        }
        
        // 注释掉医院数据加载，因为现在只是显示入口
        // this.loadHospitalData();
        
        // 新增：页面从后台恢复时，恢复轮播图自动播放
        if (this.data.isPageHidden) {
            console.log('[LOG] 页面从后台恢复，恢复轮播图自动播放');
            this.setData({
                swiperAutoplay: true,
                isPageHidden: false
            });
        }
        
        // 加载公开陪诊日记
        this.loadPublicDiaries();
    },
    
    // 新增：页面隐藏时的处理
    onHide() {
        console.log('[LOG] 页面隐藏，暂停轮播图自动播放');
        this.setData({
            swiperAutoplay: false,
            isPageHidden: true
        });
    },
    
    // 检查登录状态
    checkLoginStatus() {
        const isLoggedIn = auth.isLoggedIn();
        const userInfo = auth.getUserInfo();
        console.log('[LOG] checkLoginStatus 用户类型:', userInfo && userInfo.user_type);
        this.setData({
            isLoggedIn,
            userInfo: userInfo || {}
        });
    },
    
    // 跳转到登录页（已弃用，改为显示提示）
    goLogin() {
        wx.showToast({ 
            title: '请等待自动登录或稍后重试', 
            icon: 'none',
            duration: 2000
        });
    },
    
    // 跳转到注册页
    goRegister() {
        wx.navigateTo({ url: '/pages/register/register' });
    },
    showPrivacyWin() {
        this.trdPrivacy.showPrivacyWin();
    },
    clickHandle(e) {
        let { name, path = '' } = e.detail.item;
        if (!path) {
            name = name.replace(/^[A-Z]/, (match) => `${match}`.toLocaleLowerCase());
            name = name.replace(/[A-Z]/g, (match) => {
                return `-${match.toLowerCase()}`;
            });
            path = `/pages/${name}/${this.skyline ? 'skyline/' : ''}${name}`;
        }
        wx.navigateTo({
            url: path,
            fail: () => {
                wx.navigateTo({
                    url: '/pages/home/<USER>/navigateFail',
                });
            },
        });
    },
    onShareAppMessage() {
        return {
            title: 'TDesign UI',
            path: '/pages/home/<USER>',
        };
    },
    getQueryByUrl(url) {
        const data = {};
        const queryArr = `${url}`.match(/([^=&#?]+)=[^&#]+/g) || [];
        if (queryArr.length) {
            queryArr.forEach((para) => {
                const d = para.split('=');
                const val = decodeURIComponent(d[1]);
                if (data[d[0]] !== undefined) {
                    data[d[0]] += `,${val}`;
                }
                else {
                    data[d[0]] = val;
                }
            });
        }
        return data;
    },
    goSkyline() {
        wx.navigateTo({
            url: '/pages/home/<USER>',
        });
    },
    onServiceTap(e) {
        const path = e.currentTarget.dataset.path;
        if (!path) return;

        // 套餐服务和服务流程无需登录检查
        const isPackageService = path.includes('/pages/packages/list');
        const isServiceFlow = path === '/pages/service-flow/service-flow';

        if (isPackageService || isServiceFlow) {
            this.navigateToService(path);
        } else {
            // 其他服务需要登录检查
            const result = auth.checkLoginAndShowRegisterPrompt();
            if (result) {
                this.navigateToService(path);
            }
        }
    },

    navigateToService(path) {
        if (path === 'orders') {
            wx.switchTab({ url: '/pages/orders/orders' });
        } else if (path === 'diary') {
            wx.navigateTo({ url: '/pages/diary/list' });
        } else if (path === 'services') {
            wx.navigateTo({ url: '/pages/services/list' });
        } else if (path === 'placeholder') {
            wx.showToast({ title: '功能开发中', icon: 'none' });
        } else {
            wx.navigateTo({ url: path });
        }
    },

    goMessage() {
        wx.switchTab({ url: '/pages/message/message' });
    },
    // 搜索输入处理









    goHospitalList() {
        wx.navigateTo({ url: '/pages/hospital-list/hospital-list' });
    },
    goHospitalDetail(e) {
        const id = e.currentTarget.dataset.id;
        console.log('点击医院卡片，医院ID:', id);
        console.log('医院数据:', e.currentTarget.dataset);
        wx.navigateTo({ url: `/pages/hospital-detail/hospital-detail?id=${id}` });
    },
    goStaffDetail(e) {
        const id = e.currentTarget.dataset.id;
        wx.navigateTo({ url: `/pages/staff/detail?id=${id}` });
    },
    goStaffList() {
        wx.navigateTo({ url: '/pages/staff/list' });
    },
    // 退出登录
    logout() {
        wx.showModal({
            title: '退出登录',
            content: '确定要退出当前账号吗？',
            success: (res) => {
                if (res.confirm) {
                    const messageManager = require('../../utils/messageManager.js');
                    messageManager.stop();
                    wx.clearStorageSync();
                    this.setData({ isLoggedIn: false, userInfo: {} });
                    // 退出登录后返回首页，而不是登录页
                    wx.switchTab({ url: '/pages/home/<USER>' });
                }
            }
        });
    },
    onGetWxAvatar() {
        // 头像获取功能已移除
        wx.showToast({
            title: '头像获取功能已移除',
            icon: 'none',
            duration: 2000
        });
    },
    
    // 检查病患用户是否有可用的病患资料
    checkPatientAvailability() {
        console.log('[LOG] 开始检查病患资料可用性');
        
        getPatientList().then(patients => {
            console.log('[LOG] 获取到病患列表:', patients);
            
            // 检查是否有可用的病患资料
            if (!patients || patients.length === 0) {
                console.log('[LOG] 没有可用的病患资料，引导用户去病患管理');
                this.showPatientGuide();
            } else {
                console.log('[LOG] 找到可用病患资料，数量:', patients.length);
            }
        }).catch(err => {
            console.error('[LOG] 获取病患列表失败:', err);
            // 如果获取失败，也引导用户去病患管理
            this.showPatientGuide();
        });
    },
    
    // 显示病患管理引导
    showPatientGuide() {
        wx.showModal({
            title: '病患资料管理',
            content: '您还没有添加病患资料，需要先添加病患信息才能使用相关服务。是否现在去添加？',
            confirmText: '去添加',
            cancelText: '稍后',
            success: (res) => {
                if (res.confirm) {
                    console.log('[LOG] 用户确认去添加病患资料');
                    wx.navigateTo({
                        url: '/pages/patients/patients'
                    });
                } else {
                    console.log('[LOG] 用户选择稍后添加病患资料');
                }
            }
        });
    },
    
    // 加载医院数据
    loadHospitalData() {
        // 只为非staff用户加载医院数据
        if (auth.getUserInfo() && auth.getUserInfo().user_type === 'medical_staff') {
            return;
        }
        
        const authUtil = require('../../utils/auth.js');
        authUtil.requestWithToken({
            url: `${API_BASE}/medical/hospitals`,
            method: 'GET',
            data: { page: 1, limit: 5 },
            success: (res) => {
                console.log('首页获取医院列表成功:', res.data);
                if (res.data && res.data.success && Array.isArray(res.data.data.hospitals)) {
                    console.log('医院数据:', res.data.data.hospitals);
                    // 为每个医院添加默认图片
                    const hospitalsWithImages = res.data.data.hospitals.map(hospital => ({
                        ...hospital,
                        image: hospital.image || 'https://cdn.pixabay.com/photo/2017/05/29/20/46/hospital-2354843_960_720.jpg'
                    }));
                    this.setData({ hospitals: hospitalsWithImages });
                }
            },
            fail: (err) => {
                console.error('首页获取医院列表失败:', err);
            }
        });
    },
    
    // 加载陪诊师数据
    loadStaffData() {
        // 只为非staff用户加载陪诊师数据
        if (auth.getUserInfo() && auth.getUserInfo().user_type === 'medical_staff') {
            return;
        }
        
        const authUtil = require('../../utils/auth.js');
        authUtil.requestWithToken({
            url: `${API_BASE}/medical/medical_staff`,
            method: 'GET',
            data: { type: 'companion', page: 1, limit: 5 },
            success: (res) => {
                if (res.data && res.data.success && res.data.data && Array.isArray(res.data.data.medicalStaff)) {
                    this.setData({ staffs: res.data.data.medicalStaff });
                }
            },
            fail: (err) => {
                console.error('首页获取陪诊师列表失败:', err);
            }
        });
    },
    
    // 轮播图事件处理
    onSwiperChange(e) {
        const { current } = e.detail;
        // 添加防抖处理，避免频繁更新状态
        if (this.swiperChangeTimer) {
            clearTimeout(this.swiperChangeTimer);
        }
        
        this.swiperChangeTimer = setTimeout(() => {
            this.setData({
                swiperCurrent: current
            });
        }, 50);
    },
    
    // 轮播图准备完成
    onSwiperReady() {
        this.setData({
            swiperReady: true,
            swiperInitialized: true
        });
    },
    
    // 注释掉：优化轮播图重置逻辑（避免抖动）
    // 这个方法可能导致轮播图抖动，改为使用onHide/onShow控制自动播放
    /*
    optimizedSwiperReset() {
        const now = Date.now();
        const timeSinceLastShow = now - this.data.lastShowTime;
        
        // 如果轮播图已经初始化且距离上次显示时间很短（小于2秒），则不重置
        if (this.data.swiperInitialized && timeSinceLastShow < 2000) {
            console.log('[LOG] 轮播图短时间内返回，跳过重置避免抖动');
            return;
        }
        
        // 清除之前的定时器
        if (this.data.swiperResetTimer) {
            clearTimeout(this.data.swiperResetTimer);
        }
        
        // 设置新的定时器，使用更长的延迟时间避免抖动
        const timer = setTimeout(() => {
            this.setData({
                swiperCurrent: 0,
                swiperResetTimer: null,
                lastShowTime: now
            });
            console.log('[LOG] 轮播图重置完成');
        }, 300); // 增加到300ms延迟
        
        this.setData({
            swiperResetTimer: timer
        });
    },
    */
    
    // 注释掉：重置轮播图位置（防抖处理）- 保留原方法作为备用
    // 这个方法也可能导致轮播图抖动，改为使用onHide/onShow控制自动播放
    /*
    resetSwiperPosition() {
        // 清除之前的定时器
        if (this.data.swiperResetTimer) {
            clearTimeout(this.data.swiperResetTimer);
        }
        
        // 设置新的定时器，延迟重置避免抖动
        const timer = setTimeout(() => {
            this.setData({
                swiperCurrent: 0,
                swiperResetTimer: null
            });
        }, 100);
        
        this.setData({
            swiperResetTimer: timer
        });
    },
    */
    
    onSwiperClick(e) {
        const { index } = e.detail;
        console.log('轮播图点击:', index);
        // 这里可以添加轮播图点击跳转逻辑
    },
    
    // 页面卸载时清理定时器
    onUnload() {
        if (this.data.swiperResetTimer) {
            clearTimeout(this.data.swiperResetTimer);
        }
        if (this.swiperChangeTimer) {
            clearTimeout(this.swiperChangeTimer);
        }
    },
    
    // 加载动态（admin用户的公开日记）
    loadPublicDiaries() {
        this.setData({ diaryLoading: true });
        
        const { getDiaries } = require('../../api/diaries.js');
        
        // 直接查询用户ID为1的公开陪诊日记
        getDiaries({
            privacyLevel: 'public',
            userId: 1, // 使用用户ID为1
            limit: 3, // 只显示3条
            page: 1
        }).then(res => {
            console.log('首页获取动态成功:', res);
            if (res && res.diaries) {
                // 处理日记数据，添加默认图片
                const processedDiaries = res.diaries.map(diary => ({
                    ...diary,
                    // 统一ID字段
                    id: diary.id || diary.diary_id,
                    // 如果有附件图片，使用第一张作为缩略图
                    thumbnail: diary.attachments && diary.attachments.length > 0 
                        ? diary.attachments[0] 
                        : '/assets/medical-5459653_960_720.webp',
                    // 格式化日期
                    formattedDate: this.formatDate(diary.createdAt || diary.created_at)
                }));
                
                this.setData({ 
                    diaries: processedDiaries,
                    diaryLoading: false 
                });
            }
        }).catch(err => {
            console.error('首页获取动态失败:', err);
            this.setData({ diaryLoading: false });
        });
    },
    
    // 格式化日期
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    },
    
    // 跳转到动态列表
    goDiaryList() {
        wx.navigateTo({ url: '/pages/news/list' });
    },
    
    // 跳转到陪诊日记详情
    goDiaryDetail(e) {
        const diaryId = e.currentTarget.dataset.id;
        wx.navigateTo({ url: `/pages/diary/detail?id=${diaryId}` });
    },
});
