<!-- 全局导航栏 -->
<global-navbar 
  title="我的" 
  showBack="{{false}}"
  textColor="#ffffff"
  backgroundColor="#0C4147"
>
  <view slot="right">
    <image class="navbar-avatar" src="{{user.avatar}}" />
  </view>
</global-navbar>

<view class="mine-container">
  <!-- 顶部边距，与全局导航栏高度相等 -->
  <view class="m3-top-margin"></view>
  
  <!-- 顶部深色背景区域 -->
  <view class="m3-header-section">
    <view class="m3-user-section">
      <view class="m3-user-avatar-section">
        <image class="m3-user-avatar" src="{{user.avatar}}" />
        <view class="m3-avatar-badge" wx:if="{{user.user_type}}">
          <text class="m3-badge-icon">{{user.user_type == 'medical_staff' ? '👩‍⚕️' : (user.user_type == 'patient' ? '👤' : (user.user_type == 'family' ? '👨‍👩‍👧‍👦' : '👤'))}}</text>
        </view>
      </view>
      <view class="m3-user-info">
        <view class="m3-user-name">
          {{user.name || '未登录'}}
          <!-- 未登录时显示注册按钮 -->
          <button class="m3-register-btn" bindtap="goRegister" wx:if="{{!isLoggedIn}}">
            <t-icon name="user-add" size="20rpx" color="#ffffff" />
            <text>注册</text>
          </button>
        </view>
        <view class="m3-user-phone" wx:if="{{user.phone}}">{{user.phone}}</view>
        <view class="m3-user-type" wx:if="{{user.user_type}}">
          <text class="m3-type-badge">{{user.user_type == 'medical_staff' ? '医护人员' : (user.user_type == 'patient' ? '患者' : (user.user_type == 'family' ? '家属' : user.user_type))}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 主内容区域（浅色背景） -->
  <view class="m3-content-area">
    <!-- 功能列表 -->
    <view class="m3-function-list">
      <!-- 个人管理 - 仅对非staff用户显示 -->
      <view class="m3-card" wx:if="{{user.user_type !== 'staff' && user.user_type !== 'medical_staff'}}">
        <view class="m3-card-title">个人管理</view>
        <navigator url="/pages/patients/patients">
          <view class="m3-function-item">
            <view class="m3-item-icon">
              <t-icon name="user" size="32rpx" color="#68C4C1" />
            </view>
            <view class="m3-item-content">
              <view class="m3-item-title">病患资料</view>
              <view class="m3-item-desc">管理个人信息和设置</view>
            </view>
            <view class="m3-item-arrow">
              <t-icon name="chevron-right" size="24rpx" color="#999" />
            </view>
          </view>
        </navigator>
      </view>

      <!-- 订单服务 -->
      <view class="m3-card">
        <view class="m3-card-title">订单服务</view>
        <view class="m3-function-item" bindtap="goOrders">
          <view class="m3-item-icon">
            <t-icon name="list" size="32rpx" color="#68C4C1" />
          </view>
          <view class="m3-item-content">
            <view class="m3-item-title">我的订单</view>
            <view class="m3-item-desc">查看所有订单记录</view>
          </view>
          <view class="m3-item-arrow">
            <t-icon name="chevron-right" size="24rpx" color="#999" />
          </view>
        </view>
        <!-- 健康日记 - 仅对非staff用户显示 -->
        <view class="m3-function-item" bindtap="goDiary" wx:if="{{user.user_type !== 'staff' && user.user_type !== 'medical_staff'}}">
          <view class="m3-item-icon">
            <t-icon name="edit" size="32rpx" color="#68C4C1" />
          </view>
          <view class="m3-item-content">
            <view class="m3-item-title">健康日记</view>
            <view class="m3-item-desc">记录健康数据</view>
          </view>
          <view class="m3-item-arrow">
            <t-icon name="chevron-right" size="24rpx" color="#999" />
          </view>
        </view>
      </view>

      <!-- 其他功能 -->
      <view class="m3-card">
        <view class="m3-card-title">其他</view>
        <!-- 未登录时显示注册入口 -->
        <view class="m3-function-item" bindtap="goRegister" wx:if="{{!isLoggedIn}}">
          <view class="m3-item-icon">
            <t-icon name="user-add" size="32rpx" color="#68C4C1" />
          </view>
          <view class="m3-item-content">
            <view class="m3-item-title">用户注册</view>
            <view class="m3-item-desc">创建账号开始使用服务</view>
          </view>
          <view class="m3-item-arrow">
            <t-icon name="chevron-right" size="24rpx" color="#999" />
          </view>
        </view>
        <view class="m3-function-item" bindtap="goSettings">
          <view class="m3-item-icon">
            <t-icon name="setting" size="32rpx" color="#68C4C1" />
          </view>
          <view class="m3-item-content">
            <view class="m3-item-title">设置</view>
            <view class="m3-item-desc">应用设置和偏好</view>
          </view>
          <view class="m3-item-arrow">
            <t-icon name="chevron-right" size="24rpx" color="#999" />
          </view>
        </view>
        <view class="m3-function-item" bindtap="goHelp">
          <view class="m3-item-icon">
            <t-icon name="help-circle" size="32rpx" color="#68C4C1" />
          </view>
          <view class="m3-item-content">
            <view class="m3-item-title">帮助中心</view>
            <view class="m3-item-desc">常见问题和客服</view>
          </view>
          <view class="m3-item-arrow">
            <t-icon name="chevron-right" size="24rpx" color="#999" />
          </view>
        </view>
        <view class="m3-function-item" bindtap="goAbout">
          <view class="m3-item-icon">
            <t-icon name="info-circle" size="32rpx" color="#68C4C1" />
          </view>
          <view class="m3-item-content">
            <view class="m3-item-title">关于我们</view>
            <view class="m3-item-desc">版本信息和公司介绍</view>
          </view>
          <view class="m3-item-arrow">
            <t-icon name="chevron-right" size="24rpx" color="#999" />
          </view>
        </view>
      </view>

      <!-- 退出登录按钮 -->
      <view class="logout-btn" bindtap="onLogout" wx:if="{{isLoggedIn}}">
        退出登录
      </view>
    </view>
  </view>
</view>