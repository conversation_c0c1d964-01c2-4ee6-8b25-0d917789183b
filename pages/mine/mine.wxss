/* 我的页面容器 */
.mine-container {
  min-height: 100vh;
  background: #F5F5F5;
}

/* 导航栏头像样式 */
.navbar-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #fff;
  border: 2rpx solid #0C4147;
}

/* 顶部边距，与全局导航栏高度相等 */
.m3-top-margin {
  height: 138rpx;
  padding-top: env(safe-area-inset-top);
}

/* 顶部深色背景区域 */
.m3-header-section {
  background: #0C4147;
  padding: 40rpx 48rpx 40rpx 48rpx;
  position: relative;
  z-index: 2;
}

.m3-user-section {
  display: flex;
  align-items: center;
  padding: 0;
}

.m3-user-avatar-section {
  position: relative;
  margin-right: 32rpx;
}

.m3-user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  background: #f5f5f5;
}

.m3-avatar-badge {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  background: #fff;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.m3-badge-icon {
  font-size: 24rpx;
}

.m3-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.m3-user-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.m3-user-phone {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.m3-user-type {
  margin-top: 4rpx;
}

.m3-type-badge {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 600;
  backdrop-filter: blur(8rpx);
}

/* 主内容区域（浅色背景） */
.m3-content-area {
  background: #F5F5F5;
  min-height: 100vh;
  margin-top: -20rpx;
  padding: 40rpx 48rpx 32rpx 48rpx;
  z-index: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  border-radius: 32rpx 32rpx 0 0;
}

/* 卡片 */
.m3-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(12, 65, 71, 0.08);
  padding: 32rpx 24rpx;
}

.m3-card-title {
  font-size: 24rpx;
  color: #0C4147;
  font-weight: 700;
  margin-bottom: 16rpx;
}

/* 功能列表 */
.m3-function-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.m3-function-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.2s ease;
}

.m3-function-item:last-child {
  border-bottom: none;
}

.m3-function-item:active {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin: 0 -16rpx;
  padding: 20rpx 16rpx;
}

.m3-item-icon {
  width: 48rpx;
  height: 48rpx;
  background: #F5F5F5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.m3-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.m3-item-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.3;
}

.m3-item-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
}

.m3-item-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .user-section {
    flex-direction: column;
    text-align: center;
    gap: 24rpx;
  }
  
  .user-avatar-section {
    margin-right: 0;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.m3-card {
  animation: fadeInUp 0.6s ease-out;
}

.m3-card:nth-child(2) {
  animation-delay: 0.1s;
}

.m3-card:nth-child(3) {
  animation-delay: 0.2s;
}

/* 注册按钮样式 */
.m3-register-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  backdrop-filter: blur(8rpx);
  transition: all 0.2s ease;
  min-height: 44rpx;
  width: fit-content;
  line-height: 1;
  margin: 0;
}

.m3-register-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);
}

.m3-register-btn text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 退出登录按钮 */
.logout-btn {
  text-align: center;
  color: #ff4757;
  font-size: 28rpx;
  padding: 32rpx;
  margin-top: 24rpx;
}