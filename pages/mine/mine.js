const auth = require('../../utils/auth.js');
const navbarBehavior = require('../../behaviors/navbar.js');

Page({
  behaviors: [navbarBehavior],
  
  data: {
    user: null,
    isLoggedIn: false
  },

  onLoad() {
    this.checkLoginStatus();
  },

  onShow() {
    this.checkLoginStatus();
    
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      const userInfo = wx.getStorageSync('userInfo') || {};
      const isStaff = userInfo.user_type === 'staff' || userInfo.user_type === 'medical_staff';
      // staff用户：工作台(0)、订单(1)、消息(2)、我的(3)
      // 普通用户：首页(0)、订单(1)、消息(2)、我的(3)
      const selected = isStaff ? 3 : 3;
      this.getTabBar().setData({
        selected
      });
    }
  },



  // 检查登录状态
  checkLoginStatus() {
    const isLoggedIn = auth.isLoggedIn();
    const userRaw = auth.getUserInfo() || {};
    const user = {
      name: userRaw.real_name || userRaw.username || '',
      phone: userRaw.phone || userRaw.mobile || '',
      user_type: userRaw.user_type || '',
      avatar: 'https://cdn.pixabay.com/photo/2017/07/18/23/23/user-2517430_1280.png'
    };
    this.setData({
      isLoggedIn,
      user: isLoggedIn ? user : {}
    });
  },

  goLogin() {
    wx.showToast({ 
      title: '请等待自动登录或稍后重试', 
      icon: 'none',
      duration: 2000
    });
  },

  // 跳转到注册页面
  goRegister() {
    wx.navigateTo({ url: '/pages/user-register/user-register' });
  },

  goProfile() {
    if (!auth.checkLoginAndRedirect()) return;
    wx.navigateTo({ url: '/pages/profile/profile' });
  },

  goOrders() {
    if (!auth.checkLoginAndRedirect()) return;
    wx.switchTab({ url: '/pages/orders/orders' });
  },

  goAddress() {
    if (!auth.checkLoginAndRedirect()) return;
    wx.navigateTo({ url: '/pages/address/address' });
  },

  goPayment() {
    if (!auth.checkLoginAndRedirect()) return;
    wx.navigateTo({ url: '/pages/payment/payment' });
  },

  // 新增功能入口
  goDiary() {
    if (!auth.checkLoginAndRedirect()) return;
    wx.navigateTo({ url: '/pages/diary/list' });
  },

  goWallet() {
    if (!auth.checkLoginAndRedirect()) return;
    wx.showModal({
      title: '我的钱包',
      content: '钱包功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '确定'
    });
  },

  goSettings() {
    if (!auth.checkLoginAndRedirect()) return;
    wx.showModal({
      title: '设置',
      content: '设置功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '确定'
    });
  },

  goHelp() {
    // 获取客服电话配置
    const app = getApp();
    const config = app.globalData.config;
    const customerServiceNumbers = config?.customer_service_numbers || [];
    
    if (customerServiceNumbers.length === 0) {
      wx.showModal({
        title: '帮助中心',
        content: '如有问题请联系客服',
        showCancel: false,
        confirmText: '确定'
      });
      return;
    }
    
    // 构建客服电话列表
    const phoneList = customerServiceNumbers.map(phone => `📞 ${phone}`).join('\n');
    const content = `如有问题请联系客服：\n\n${phoneList}`;
    
    wx.showModal({
      title: '帮助中心',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  goAbout() {
    wx.showModal({
      title: '关于我们',
      content: '上海馨守科技 | 健康守护者\n\n科技赋能 · 专业陪诊 · 全程关怀\n\n我们是谁\n\n上海馨守科技（普陀区）成立于2025年，专注"科技+健康服务"，以数字化技术优化陪诊陪护体验，提供专业、高效的医疗陪伴与健康管理。',
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？将清除所有缓存并重新加载小程序。',
      success: (res) => {
        if (res.confirm) {
          // 停止消息管理器
          const messageManager = require('../../utils/messageManager.js');
          messageManager.stop();

          // 清除所有缓存
          wx.clearStorageSync();

          // 显示退出提示
          wx.showToast({
            title: '已退出登录',
            icon: 'success',
            duration: 1500
          });

          // 延迟重新加载小程序到首页并触发静默登录
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/home/<USER>',
              success: () => {
                // 手动触发静默登录
                const app = getApp();
                if (app && app.silentLogin) {
                  console.log('[退出登录] 手动触发静默登录');
                  app.silentLogin();
                }
              }
            });
          }, 1500);
        }
      }
    });
  }
}); 